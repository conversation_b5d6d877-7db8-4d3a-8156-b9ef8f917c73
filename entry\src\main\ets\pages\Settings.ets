import router from '@ohos.router';
import { ReadingSettings } from '../common/models/ReadingSettings';
import { PreferencesManager } from '../common/preferences/PreferencesManager';

@Entry
@Component
struct Settings {
  @State readingSettings: ReadingSettings = new ReadingSettings();
  @State appVersion: string = '1.0.0';
  @State autoBackup: boolean = false;
  @State themeMode: string = 'auto';
  @State language: string = 'zh-CN';
  private preferencesManager: PreferencesManager = PreferencesManager.getInstance();

  aboutToAppear() {
    this.loadSettings();
  }

  async loadSettings() {
    try {
      this.readingSettings = await this.preferencesManager.getReadingSettings();
      this.appVersion = await this.preferencesManager.getAppVersion();
      this.autoBackup = await this.preferencesManager.getAutoBackup();
      this.themeMode = await this.preferencesManager.getThemeMode();
      this.language = await this.preferencesManager.getLanguage();
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  }

  build() {
    Column() {
      // 顶部标题栏
      Row() {
        Button('返回')
          .onClick(() => {
            router.back();
          })
          .backgroundColor(Color.Transparent)
          .fontColor($r('app.color.primary_color'))

        Text('设置')
          .fontSize(20)
          .fontWeight(FontWeight.Bold)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        // 占位，保持标题居中
        Text('')
          .width(48)
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor($r('app.color.card_background'))

      Scroll() {
        Column() {
          // 阅读设置
          this.buildSectionTitle('阅读设置')
          this.buildSettingItem('字体大小', `${this.readingSettings.fontSize}px`, () => {
            this.showFontSizeDialog();
          })
          this.buildSettingItem('阅读主题', this.getThemeDisplayName(this.readingSettings.theme), () => {
            this.showThemeDialog();
          })
          this.buildSwitchItem('保持屏幕常亮', this.readingSettings.keepScreenOn, (value) => {
            this.readingSettings.keepScreenOn = value;
            this.saveReadingSettings();
          })
          this.buildSwitchItem('音量键翻页', this.readingSettings.volumeKeyTurnPage, (value) => {
            this.readingSettings.volumeKeyTurnPage = value;
            this.saveReadingSettings();
          })
          this.buildSwitchItem('翻页动画', this.readingSettings.animationEnabled, (value) => {
            this.readingSettings.animationEnabled = value;
            this.saveReadingSettings();
          })

          Divider()
            .margin({ top: 16, bottom: 16 })
            .color($r('app.color.divider_color'))

          // 应用设置
          this.buildSectionTitle('应用设置')
          this.buildSettingItem('应用主题', this.getAppThemeDisplayName(this.themeMode), () => {
            this.showAppThemeDialog();
          })
          this.buildSettingItem('语言设置', this.getLanguageDisplayName(this.language), () => {
            this.showLanguageDialog();
          })
          this.buildSwitchItem('自动备份', this.autoBackup, (value) => {
            this.autoBackup = value;
            this.saveAutoBackup();
          })

          Divider()
            .margin({ top: 16, bottom: 16 })
            .color($r('app.color.divider_color'))

          // 数据管理
          this.buildSectionTitle('数据管理')
          this.buildSettingItem('导出数据', '', () => {
            this.exportData();
          })
          this.buildSettingItem('导入数据', '', () => {
            this.importData();
          })
          this.buildSettingItem('清除缓存', '', () => {
            this.clearCache();
          })

          Divider()
            .margin({ top: 16, bottom: 16 })
            .color($r('app.color.divider_color'))

          // 关于
          this.buildSectionTitle('关于')
          this.buildSettingItem('版本信息', `v${this.appVersion}`, () => {
            this.showVersionInfo();
          })
          this.buildSettingItem('用户协议', '', () => {
            this.showUserAgreement();
          })
          this.buildSettingItem('隐私政策', '', () => {
            this.showPrivacyPolicy();
          })
          this.buildSettingItem('意见反馈', '', () => {
            this.showFeedback();
          })
        }
        .padding({ left: 16, right: 16, top: 16, bottom: 32 })
      }
      .layoutWeight(1)
    }
    .width('100%')
    .height('100%')
    .backgroundColor($r('app.color.background_color'))
  }

  @Builder buildSectionTitle(title: string) {
    Text(title)
      .fontSize(16)
      .fontWeight(FontWeight.Medium)
      .fontColor($r('app.color.text_primary'))
      .margin({ bottom: 12 })
  }

  @Builder buildSettingItem(title: string, value: string, onClick: () => void) {
    Row() {
      Text(title)
        .fontSize(16)
        .fontColor($r('app.color.text_primary'))
        .layoutWeight(1)

      if (value) {
        Text(value)
          .fontSize(14)
          .fontColor($r('app.color.text_secondary'))
          .margin({ right: 8 })
      }

      Image($r('app.media.arrow_right'))
        .width(16)
        .height(16)
        .fillColor($r('app.color.text_secondary'))
    }
    .width('100%')
    .height(48)
    .padding({ left: 16, right: 16 })
    .backgroundColor($r('app.color.card_background'))
    .borderRadius(8)
    .margin({ bottom: 8 })
    .onClick(onClick)
  }

  @Builder buildSwitchItem(title: string, value: boolean, onChange: (value: boolean) => void) {
    Row() {
      Text(title)
        .fontSize(16)
        .fontColor($r('app.color.text_primary'))
        .layoutWeight(1)

      Toggle({ type: ToggleType.Switch, isOn: value })
        .onChange((isOn: boolean) => {
          onChange(isOn);
        })
        .selectedColor($r('app.color.primary_color'))
    }
    .width('100%')
    .height(48)
    .padding({ left: 16, right: 16 })
    .backgroundColor($r('app.color.card_background'))
    .borderRadius(8)
    .margin({ bottom: 8 })
  }

  getThemeDisplayName(theme: string): string {
    switch (theme) {
      case 'light': return '日间模式';
      case 'dark': return '夜间模式';
      case 'sepia': return '护眼模式';
      default: return '日间模式';
    }
  }

  getAppThemeDisplayName(theme: string): string {
    switch (theme) {
      case 'light': return '浅色';
      case 'dark': return '深色';
      case 'auto': return '跟随系统';
      default: return '跟随系统';
    }
  }

  getLanguageDisplayName(language: string): string {
    switch (language) {
      case 'zh-CN': return '简体中文';
      case 'zh-TW': return '繁体中文';
      case 'en-US': return 'English';
      default: return '简体中文';
    }
  }

  showFontSizeDialog() {
    // 显示字体大小选择对话框
    AlertDialog.show({
      title: '字体大小',
      message: '请选择合适的字体大小',
      primaryButton: {
        value: '确定',
        action: () => {
          this.saveReadingSettings();
        }
      }
    });
  }

  showThemeDialog() {
    // 显示主题选择对话框
    ActionSheet.show({
      title: '选择阅读主题',
      message: '请选择您喜欢的阅读主题',
      sheets: [
        { title: '日间模式', action: () => { this.readingSettings.theme = 'light'; this.saveReadingSettings(); } },
        { title: '夜间模式', action: () => { this.readingSettings.theme = 'dark'; this.saveReadingSettings(); } },
        { title: '护眼模式', action: () => { this.readingSettings.theme = 'sepia'; this.saveReadingSettings(); } }
      ]
    });
  }

  showAppThemeDialog() {
    ActionSheet.show({
      title: '选择应用主题',
      message: '请选择应用的主题模式',
      sheets: [
        { title: '浅色', action: () => { this.themeMode = 'light'; this.saveThemeMode(); } },
        { title: '深色', action: () => { this.themeMode = 'dark'; this.saveThemeMode(); } },
        { title: '跟随系统', action: () => { this.themeMode = 'auto'; this.saveThemeMode(); } }
      ]
    });
  }

  showLanguageDialog() {
    ActionSheet.show({
      title: '选择语言',
      message: '请选择应用语言',
      sheets: [
        { title: '简体中文', action: () => { this.language = 'zh-CN'; this.saveLanguage(); } },
        { title: '繁体中文', action: () => { this.language = 'zh-TW'; this.saveLanguage(); } },
        { title: 'English', action: () => { this.language = 'en-US'; this.saveLanguage(); } }
      ]
    });
  }

  async saveReadingSettings() {
    try {
      await this.preferencesManager.saveReadingSettings(this.readingSettings);
    } catch (error) {
      console.error('Failed to save reading settings:', error);
    }
  }

  async saveAutoBackup() {
    try {
      await this.preferencesManager.saveAutoBackup(this.autoBackup);
    } catch (error) {
      console.error('Failed to save auto backup setting:', error);
    }
  }

  async saveThemeMode() {
    try {
      await this.preferencesManager.saveThemeMode(this.themeMode);
    } catch (error) {
      console.error('Failed to save theme mode:', error);
    }
  }

  async saveLanguage() {
    try {
      await this.preferencesManager.saveLanguage(this.language);
    } catch (error) {
      console.error('Failed to save language:', error);
    }
  }

  exportData() {
    // 实现数据导出功能
    console.info('Export data');
  }

  importData() {
    // 实现数据导入功能
    console.info('Import data');
  }

  clearCache() {
    // 实现清除缓存功能
    AlertDialog.show({
      title: '清除缓存',
      message: '确定要清除所有缓存数据吗？',
      primaryButton: {
        value: '确定',
        action: () => {
          console.info('Clear cache');
        }
      },
      secondaryButton: {
        value: '取消',
        action: () => {}
      }
    });
  }

  showVersionInfo() {
    AlertDialog.show({
      title: '版本信息',
      message: `Seeree阅读器 v${this.appVersion}\n\n一款专为鸿蒙系统设计的跨端阅读软件`,
      primaryButton: {
        value: '确定',
        action: () => {}
      }
    });
  }

  showUserAgreement() {
    console.info('Show user agreement');
  }

  showPrivacyPolicy() {
    console.info('Show privacy policy');
  }

  showFeedback() {
    console.info('Show feedback');
  }
}
