import preferences from '@ohos.data.preferences';
import { ReadingSettings } from '../models/ReadingSettings';
import common from '@ohos.app.ability.common';

/**
 * 首选项管理器
 */
export class PreferencesManager {
  private static instance: PreferencesManager;
  private preferencesStore: preferences.Preferences | null = null;
  private readonly PREFERENCES_NAME = 'SeereeReaderPrefs';

  // 首选项键名常量
  private readonly KEYS = {
    READING_SETTINGS: 'reading_settings',
    LAST_READ_BOOK_ID: 'last_read_book_id',
    APP_VERSION: 'app_version',
    FIRST_LAUNCH: 'first_launch',
    AUTO_BACKUP: 'auto_backup',
    BACKUP_INTERVAL: 'backup_interval',
    THEME_MODE: 'theme_mode',
    LANGUAGE: 'language'
  };

  private constructor() {}

  static getInstance(): PreferencesManager {
    if (!PreferencesManager.instance) {
      PreferencesManager.instance = new PreferencesManager();
    }
    return PreferencesManager.instance;
  }

  /**
   * 初始化首选项
   */
  async initPreferences(context: common.UIAbilityContext): Promise<void> {
    try {
      this.preferencesStore = await preferences.getPreferences(context, this.PREFERENCES_NAME);
      console.info('Preferences initialized successfully');
    } catch (error) {
      console.error('Failed to initialize preferences:', error);
    }
  }

  /**
   * 保存阅读设置
   */
  async saveReadingSettings(settings: ReadingSettings): Promise<void> {
    if (!this.preferencesStore) {
      throw new Error('Preferences not initialized');
    }

    try {
      const settingsJson = JSON.stringify(settings.toStorageObject());
      await this.preferencesStore.put(this.KEYS.READING_SETTINGS, settingsJson);
      await this.preferencesStore.flush();
    } catch (error) {
      console.error('Failed to save reading settings:', error);
      throw error;
    }
  }

  /**
   * 获取阅读设置
   */
  async getReadingSettings(): Promise<ReadingSettings> {
    if (!this.preferencesStore) {
      throw new Error('Preferences not initialized');
    }

    try {
      const settingsJson = await this.preferencesStore.get(this.KEYS.READING_SETTINGS, '{}') as string;
      const settingsData = JSON.parse(settingsJson);
      return ReadingSettings.fromStorageObject(settingsData);
    } catch (error) {
      console.error('Failed to get reading settings:', error);
      return new ReadingSettings(); // 返回默认设置
    }
  }

  /**
   * 保存最后阅读的书籍ID
   */
  async saveLastReadBookId(bookId: number): Promise<void> {
    if (!this.preferencesStore) {
      throw new Error('Preferences not initialized');
    }

    try {
      await this.preferencesStore.put(this.KEYS.LAST_READ_BOOK_ID, bookId);
      await this.preferencesStore.flush();
    } catch (error) {
      console.error('Failed to save last read book id:', error);
      throw error;
    }
  }

  /**
   * 获取最后阅读的书籍ID
   */
  async getLastReadBookId(): Promise<number> {
    if (!this.preferencesStore) {
      throw new Error('Preferences not initialized');
    }

    try {
      return await this.preferencesStore.get(this.KEYS.LAST_READ_BOOK_ID, 0) as number;
    } catch (error) {
      console.error('Failed to get last read book id:', error);
      return 0;
    }
  }

  /**
   * 保存应用版本
   */
  async saveAppVersion(version: string): Promise<void> {
    if (!this.preferencesStore) {
      throw new Error('Preferences not initialized');
    }

    try {
      await this.preferencesStore.put(this.KEYS.APP_VERSION, version);
      await this.preferencesStore.flush();
    } catch (error) {
      console.error('Failed to save app version:', error);
      throw error;
    }
  }

  /**
   * 获取应用版本
   */
  async getAppVersion(): Promise<string> {
    if (!this.preferencesStore) {
      throw new Error('Preferences not initialized');
    }

    try {
      return await this.preferencesStore.get(this.KEYS.APP_VERSION, '1.0.0') as string;
    } catch (error) {
      console.error('Failed to get app version:', error);
      return '1.0.0';
    }
  }

  /**
   * 检查是否首次启动
   */
  async isFirstLaunch(): Promise<boolean> {
    if (!this.preferencesStore) {
      throw new Error('Preferences not initialized');
    }

    try {
      return await this.preferencesStore.get(this.KEYS.FIRST_LAUNCH, true) as boolean;
    } catch (error) {
      console.error('Failed to check first launch:', error);
      return true;
    }
  }

  /**
   * 设置首次启动标记
   */
  async setFirstLaunchCompleted(): Promise<void> {
    if (!this.preferencesStore) {
      throw new Error('Preferences not initialized');
    }

    try {
      await this.preferencesStore.put(this.KEYS.FIRST_LAUNCH, false);
      await this.preferencesStore.flush();
    } catch (error) {
      console.error('Failed to set first launch completed:', error);
      throw error;
    }
  }

  /**
   * 保存自动备份设置
   */
  async saveAutoBackup(enabled: boolean): Promise<void> {
    if (!this.preferencesStore) {
      throw new Error('Preferences not initialized');
    }

    try {
      await this.preferencesStore.put(this.KEYS.AUTO_BACKUP, enabled);
      await this.preferencesStore.flush();
    } catch (error) {
      console.error('Failed to save auto backup setting:', error);
      throw error;
    }
  }

  /**
   * 获取自动备份设置
   */
  async getAutoBackup(): Promise<boolean> {
    if (!this.preferencesStore) {
      throw new Error('Preferences not initialized');
    }

    try {
      return await this.preferencesStore.get(this.KEYS.AUTO_BACKUP, false) as boolean;
    } catch (error) {
      console.error('Failed to get auto backup setting:', error);
      return false;
    }
  }

  /**
   * 保存备份间隔（小时）
   */
  async saveBackupInterval(hours: number): Promise<void> {
    if (!this.preferencesStore) {
      throw new Error('Preferences not initialized');
    }

    try {
      await this.preferencesStore.put(this.KEYS.BACKUP_INTERVAL, hours);
      await this.preferencesStore.flush();
    } catch (error) {
      console.error('Failed to save backup interval:', error);
      throw error;
    }
  }

  /**
   * 获取备份间隔（小时）
   */
  async getBackupInterval(): Promise<number> {
    if (!this.preferencesStore) {
      throw new Error('Preferences not initialized');
    }

    try {
      return await this.preferencesStore.get(this.KEYS.BACKUP_INTERVAL, 24) as number;
    } catch (error) {
      console.error('Failed to get backup interval:', error);
      return 24;
    }
  }

  /**
   * 保存主题模式
   */
  async saveThemeMode(theme: string): Promise<void> {
    if (!this.preferencesStore) {
      throw new Error('Preferences not initialized');
    }

    try {
      await this.preferencesStore.put(this.KEYS.THEME_MODE, theme);
      await this.preferencesStore.flush();
    } catch (error) {
      console.error('Failed to save theme mode:', error);
      throw error;
    }
  }

  /**
   * 获取主题模式
   */
  async getThemeMode(): Promise<string> {
    if (!this.preferencesStore) {
      throw new Error('Preferences not initialized');
    }

    try {
      return await this.preferencesStore.get(this.KEYS.THEME_MODE, 'auto') as string;
    } catch (error) {
      console.error('Failed to get theme mode:', error);
      return 'auto';
    }
  }

  /**
   * 保存语言设置
   */
  async saveLanguage(language: string): Promise<void> {
    if (!this.preferencesStore) {
      throw new Error('Preferences not initialized');
    }

    try {
      await this.preferencesStore.put(this.KEYS.LANGUAGE, language);
      await this.preferencesStore.flush();
    } catch (error) {
      console.error('Failed to save language:', error);
      throw error;
    }
  }

  /**
   * 获取语言设置
   */
  async getLanguage(): Promise<string> {
    if (!this.preferencesStore) {
      throw new Error('Preferences not initialized');
    }

    try {
      return await this.preferencesStore.get(this.KEYS.LANGUAGE, 'zh-CN') as string;
    } catch (error) {
      console.error('Failed to get language:', error);
      return 'zh-CN';
    }
  }

  /**
   * 清除所有首选项
   */
  async clearAll(): Promise<void> {
    if (!this.preferencesStore) {
      throw new Error('Preferences not initialized');
    }

    try {
      await this.preferencesStore.clear();
      await this.preferencesStore.flush();
    } catch (error) {
      console.error('Failed to clear preferences:', error);
      throw error;
    }
  }

  /**
   * 删除指定键的首选项
   */
  async delete(key: string): Promise<void> {
    if (!this.preferencesStore) {
      throw new Error('Preferences not initialized');
    }

    try {
      await this.preferencesStore.delete(key);
      await this.preferencesStore.flush();
    } catch (error) {
      console.error(`Failed to delete preference key ${key}:`, error);
      throw error;
    }
  }
}
