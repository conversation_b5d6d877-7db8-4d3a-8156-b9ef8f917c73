import relationalStore from '@ohos.data.relationalStore';
import { Book } from '../models/Book';
import { Bookmark, ReadingHistory } from '../models/Bookmark';

/**
 * 数据库管理器
 */
export class DatabaseManager {
  private static instance: DatabaseManager;
  private rdbStore: relationalStore.RdbStore | null = null;
  private readonly DB_NAME = 'SeereeReader.db';
  private readonly DB_VERSION = 1;

  private constructor() {}

  static getInstance(): DatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager();
    }
    return DatabaseManager.instance;
  }

  /**
   * 初始化数据库
   */
  async initDatabase(): Promise<void> {
    try {
      const config: relationalStore.StoreConfig = {
        name: this.DB_NAME,
        securityLevel: relationalStore.SecurityLevel.S1
      };

      this.rdbStore = await relationalStore.getRdbStore(globalThis.context, config);
      await this.createTables();
    } catch (error) {
      console.error('Database initialization failed:', error);
    }
  }

  /**
   * 创建数据表
   */
  private async createTables(): Promise<void> {
    if (!this.rdbStore) return;

    // 创建书籍表
    const createBooksTable = `
      CREATE TABLE IF NOT EXISTS books (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        author TEXT,
        cover TEXT,
        filePath TEXT NOT NULL UNIQUE,
        fileType TEXT NOT NULL,
        fileSize INTEGER DEFAULT 0,
        addTime INTEGER NOT NULL,
        lastReadTime INTEGER DEFAULT 0,
        readingProgress INTEGER DEFAULT 0,
        currentChapter INTEGER DEFAULT 0,
        currentPosition INTEGER DEFAULT 0,
        totalChapters INTEGER DEFAULT 0,
        description TEXT,
        category TEXT,
        tags TEXT,
        isFavorite INTEGER DEFAULT 0,
        isFinished INTEGER DEFAULT 0
      )
    `;

    // 创建书签表
    const createBookmarksTable = `
      CREATE TABLE IF NOT EXISTS bookmarks (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        bookId INTEGER NOT NULL,
        chapterIndex INTEGER NOT NULL,
        position INTEGER NOT NULL,
        content TEXT,
        note TEXT,
        createTime INTEGER NOT NULL,
        FOREIGN KEY (bookId) REFERENCES books (id) ON DELETE CASCADE
      )
    `;

    // 创建阅读历史表
    const createReadingHistoryTable = `
      CREATE TABLE IF NOT EXISTS reading_history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        bookId INTEGER NOT NULL,
        chapterIndex INTEGER NOT NULL,
        position INTEGER NOT NULL,
        readTime INTEGER NOT NULL,
        duration INTEGER DEFAULT 0,
        FOREIGN KEY (bookId) REFERENCES books (id) ON DELETE CASCADE
      )
    `;

    try {
      await this.rdbStore.executeSql(createBooksTable);
      await this.rdbStore.executeSql(createBookmarksTable);
      await this.rdbStore.executeSql(createReadingHistoryTable);
      console.info('Database tables created successfully');
    } catch (error) {
      console.error('Failed to create tables:', error);
    }
  }

  /**
   * 添加书籍
   */
  async addBook(book: Book): Promise<number> {
    if (!this.rdbStore) {
      throw new Error('Database not initialized');
    }

    try {
      const valueBucket = book.toDbObject();
      delete valueBucket.id; // 移除id，让数据库自动生成
      
      const rowId = await this.rdbStore.insert('books', valueBucket);
      return rowId;
    } catch (error) {
      console.error('Failed to add book:', error);
      throw error;
    }
  }

  /**
   * 更新书籍信息
   */
  async updateBook(book: Book): Promise<boolean> {
    if (!this.rdbStore) {
      throw new Error('Database not initialized');
    }

    try {
      const valueBucket = book.toDbObject();
      const predicates = new relationalStore.RdbPredicates('books');
      predicates.equalTo('id', book.id);
      
      const rowsAffected = await this.rdbStore.update(valueBucket, predicates);
      return rowsAffected > 0;
    } catch (error) {
      console.error('Failed to update book:', error);
      throw error;
    }
  }

  /**
   * 删除书籍
   */
  async deleteBook(bookId: number): Promise<boolean> {
    if (!this.rdbStore) {
      throw new Error('Database not initialized');
    }

    try {
      const predicates = new relationalStore.RdbPredicates('books');
      predicates.equalTo('id', bookId);
      
      const rowsAffected = await this.rdbStore.delete(predicates);
      return rowsAffected > 0;
    } catch (error) {
      console.error('Failed to delete book:', error);
      throw error;
    }
  }

  /**
   * 获取所有书籍
   */
  async getAllBooks(): Promise<Book[]> {
    if (!this.rdbStore) {
      throw new Error('Database not initialized');
    }

    try {
      const predicates = new relationalStore.RdbPredicates('books');
      predicates.orderByDesc('lastReadTime');
      
      const resultSet = await this.rdbStore.query(predicates);
      const books: Book[] = [];
      
      if (resultSet.goToFirstRow()) {
        do {
          const book = Book.fromDbObject(this.resultSetToObject(resultSet));
          books.push(book);
        } while (resultSet.goToNextRow());
      }
      
      resultSet.close();
      return books;
    } catch (error) {
      console.error('Failed to get all books:', error);
      throw error;
    }
  }

  /**
   * 根据ID获取书籍
   */
  async getBookById(bookId: number): Promise<Book | null> {
    if (!this.rdbStore) {
      throw new Error('Database not initialized');
    }

    try {
      const predicates = new relationalStore.RdbPredicates('books');
      predicates.equalTo('id', bookId);
      
      const resultSet = await this.rdbStore.query(predicates);
      let book: Book | null = null;
      
      if (resultSet.goToFirstRow()) {
        book = Book.fromDbObject(this.resultSetToObject(resultSet));
      }
      
      resultSet.close();
      return book;
    } catch (error) {
      console.error('Failed to get book by id:', error);
      throw error;
    }
  }

  /**
   * 搜索书籍
   */
  async searchBooks(keyword: string): Promise<Book[]> {
    if (!this.rdbStore) {
      throw new Error('Database not initialized');
    }

    try {
      const predicates = new relationalStore.RdbPredicates('books');
      predicates.like('title', `%${keyword}%`)
        .or()
        .like('author', `%${keyword}%`);
      
      const resultSet = await this.rdbStore.query(predicates);
      const books: Book[] = [];
      
      if (resultSet.goToFirstRow()) {
        do {
          const book = Book.fromDbObject(this.resultSetToObject(resultSet));
          books.push(book);
        } while (resultSet.goToNextRow());
      }
      
      resultSet.close();
      return books;
    } catch (error) {
      console.error('Failed to search books:', error);
      throw error;
    }
  }

  /**
   * 将ResultSet转换为对象
   */
  private resultSetToObject(resultSet: relationalStore.ResultSet): Record<string, any> {
    const columnNames = resultSet.columnNames;
    const obj: Record<string, any> = {};
    
    for (let i = 0; i < columnNames.length; i++) {
      const columnName = columnNames[i];
      const columnIndex = resultSet.getColumnIndex(columnName);
      
      switch (resultSet.getColumnType(columnIndex)) {
        case relationalStore.ColumnType.TYPE_INTEGER:
          obj[columnName] = resultSet.getLong(columnIndex);
          break;
        case relationalStore.ColumnType.TYPE_REAL:
          obj[columnName] = resultSet.getDouble(columnIndex);
          break;
        case relationalStore.ColumnType.TYPE_STRING:
          obj[columnName] = resultSet.getString(columnIndex);
          break;
        case relationalStore.ColumnType.TYPE_BLOB:
          obj[columnName] = resultSet.getBlob(columnIndex);
          break;
        default:
          obj[columnName] = null;
      }
    }
    
    return obj;
  }

  // ==================== 书签管理 ====================

  /**
   * 添加书签
   */
  async addBookmark(bookmark: Bookmark): Promise<number> {
    if (!this.rdbStore) {
      throw new Error('Database not initialized');
    }

    try {
      const valueBucket = bookmark.toDbObject();
      delete valueBucket.id; // 移除id，让数据库自动生成

      const rowId = await this.rdbStore.insert('bookmarks', valueBucket);
      return rowId;
    } catch (error) {
      console.error('Failed to add bookmark:', error);
      throw error;
    }
  }

  /**
   * 删除书签
   */
  async deleteBookmark(bookmarkId: number): Promise<boolean> {
    if (!this.rdbStore) {
      throw new Error('Database not initialized');
    }

    try {
      const predicates = new relationalStore.RdbPredicates('bookmarks');
      predicates.equalTo('id', bookmarkId);

      const rowsAffected = await this.rdbStore.delete(predicates);
      return rowsAffected > 0;
    } catch (error) {
      console.error('Failed to delete bookmark:', error);
      throw error;
    }
  }

  /**
   * 获取书籍的所有书签
   */
  async getBookmarksByBookId(bookId: number): Promise<Bookmark[]> {
    if (!this.rdbStore) {
      throw new Error('Database not initialized');
    }

    try {
      const predicates = new relationalStore.RdbPredicates('bookmarks');
      predicates.equalTo('bookId', bookId).orderByDesc('createTime');

      const resultSet = await this.rdbStore.query(predicates);
      const bookmarks: Bookmark[] = [];

      if (resultSet.goToFirstRow()) {
        do {
          const bookmark = Bookmark.fromDbObject(this.resultSetToObject(resultSet));
          bookmarks.push(bookmark);
        } while (resultSet.goToNextRow());
      }

      resultSet.close();
      return bookmarks;
    } catch (error) {
      console.error('Failed to get bookmarks:', error);
      throw error;
    }
  }

  /**
   * 更新书签
   */
  async updateBookmark(bookmark: Bookmark): Promise<boolean> {
    if (!this.rdbStore) {
      throw new Error('Database not initialized');
    }

    try {
      const valueBucket = bookmark.toDbObject();
      const predicates = new relationalStore.RdbPredicates('bookmarks');
      predicates.equalTo('id', bookmark.id);

      const rowsAffected = await this.rdbStore.update(valueBucket, predicates);
      return rowsAffected > 0;
    } catch (error) {
      console.error('Failed to update bookmark:', error);
      throw error;
    }
  }

  // ==================== 阅读历史管理 ====================

  /**
   * 添加阅读历史记录
   */
  async addReadingHistory(history: ReadingHistory): Promise<number> {
    if (!this.rdbStore) {
      throw new Error('Database not initialized');
    }

    try {
      const valueBucket = history.toDbObject();
      delete valueBucket.id; // 移除id，让数据库自动生成

      const rowId = await this.rdbStore.insert('reading_history', valueBucket);
      return rowId;
    } catch (error) {
      console.error('Failed to add reading history:', error);
      throw error;
    }
  }

  /**
   * 获取书籍的阅读历史
   */
  async getReadingHistoryByBookId(bookId: number, limit: number = 50): Promise<ReadingHistory[]> {
    if (!this.rdbStore) {
      throw new Error('Database not initialized');
    }

    try {
      const predicates = new relationalStore.RdbPredicates('reading_history');
      predicates.equalTo('bookId', bookId)
        .orderByDesc('readTime')
        .limitAs(limit);

      const resultSet = await this.rdbStore.query(predicates);
      const histories: ReadingHistory[] = [];

      if (resultSet.goToFirstRow()) {
        do {
          const history = ReadingHistory.fromDbObject(this.resultSetToObject(resultSet));
          histories.push(history);
        } while (resultSet.goToNextRow());
      }

      resultSet.close();
      return histories;
    } catch (error) {
      console.error('Failed to get reading history:', error);
      throw error;
    }
  }

  /**
   * 获取最近阅读的书籍
   */
  async getRecentBooks(limit: number = 10): Promise<Book[]> {
    if (!this.rdbStore) {
      throw new Error('Database not initialized');
    }

    try {
      const predicates = new relationalStore.RdbPredicates('books');
      predicates.greaterThan('lastReadTime', 0)
        .orderByDesc('lastReadTime')
        .limitAs(limit);

      const resultSet = await this.rdbStore.query(predicates);
      const books: Book[] = [];

      if (resultSet.goToFirstRow()) {
        do {
          const book = Book.fromDbObject(this.resultSetToObject(resultSet));
          books.push(book);
        } while (resultSet.goToNextRow());
      }

      resultSet.close();
      return books;
    } catch (error) {
      console.error('Failed to get recent books:', error);
      throw error;
    }
  }

  /**
   * 清理旧的阅读历史记录
   */
  async cleanOldReadingHistory(daysToKeep: number = 30): Promise<void> {
    if (!this.rdbStore) {
      throw new Error('Database not initialized');
    }

    try {
      const cutoffTime = Date.now() - (daysToKeep * 24 * 60 * 60 * 1000);
      const predicates = new relationalStore.RdbPredicates('reading_history');
      predicates.lessThan('readTime', cutoffTime);

      await this.rdbStore.delete(predicates);
      console.info(`Cleaned reading history older than ${daysToKeep} days`);
    } catch (error) {
      console.error('Failed to clean old reading history:', error);
    }
  }

  /**
   * 关闭数据库
   */
  async closeDatabase(): Promise<void> {
    if (this.rdbStore) {
      await this.rdbStore.close();
      this.rdbStore = null;
    }
  }
}
