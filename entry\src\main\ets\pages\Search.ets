import router from '@ohos.router';
import { Book } from '../common/models/Book';
import { DatabaseManager } from '../common/database/DatabaseManager';
import { ReaderController } from '../common/reader/ReaderController';

@Entry
@Component
struct Search {
  @State searchKeyword: string = '';
  @State searchResults: Book[] = [];
  @State contentSearchResults: Array<{book: Book, results: Array<{chapter: number, position: number, context: string}>}> = [];
  @State isLoading: boolean = false;
  @State searchType: string = 'books'; // 'books' | 'content'
  @State hasSearched: boolean = false;
  
  private databaseManager: DatabaseManager = DatabaseManager.getInstance();
  private readerController: ReaderController = new ReaderController();

  build() {
    Column() {
      // 顶部搜索栏
      this.buildSearchHeader()

      // 搜索类型切换
      this.buildSearchTypeTabs()

      // 搜索结果
      if (this.isLoading) {
        this.buildLoadingView()
      } else if (this.hasSearched) {
        if (this.searchType === 'books') {
          this.buildBookSearchResults()
        } else {
          this.buildContentSearchResults()
        }
      } else {
        this.buildEmptyView()
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor($r('app.color.background_color'))
  }

  @Builder buildSearchHeader() {
    Row() {
      Button('返回')
        .onClick(() => {
          router.back();
        })
        .backgroundColor(Color.Transparent)
        .fontColor($r('app.color.primary_color'))

      Row() {
        TextInput({ placeholder: '搜索书籍或内容...', text: this.searchKeyword })
          .onChange((value: string) => {
            this.searchKeyword = value;
          })
          .onSubmit(() => {
            this.performSearch();
          })
          .layoutWeight(1)
          .backgroundColor($r('app.color.card_background'))
          .border({ width: 1, color: $r('app.color.divider_color'), radius: 20 })

        Button('搜索')
          .onClick(() => {
            this.performSearch();
          })
          .fontSize(14)
          .backgroundColor($r('app.color.primary_color'))
          .fontColor(Color.White)
          .margin({ left: 8 })
      }
      .layoutWeight(1)
      .margin({ left: 12 })
    }
    .width('100%')
    .height(56)
    .padding({ left: 16, right: 16 })
    .backgroundColor($r('app.color.card_background'))
  }

  @Builder buildSearchTypeTabs() {
    Row() {
      Button('搜索书籍')
        .onClick(() => {
          this.searchType = 'books';
          if (this.hasSearched) {
            this.performSearch();
          }
        })
        .fontSize(14)
        .backgroundColor(this.searchType === 'books' ? $r('app.color.primary_color') : Color.Transparent)
        .fontColor(this.searchType === 'books' ? Color.White : $r('app.color.text_primary'))
        .border({
          width: 1,
          color: this.searchType === 'books' ? $r('app.color.primary_color') : $r('app.color.divider_color')
        })
        .borderRadius(16)
        .padding({ left: 16, right: 16, top: 8, bottom: 8 })

      Button('搜索内容')
        .onClick(() => {
          this.searchType = 'content';
          if (this.hasSearched) {
            this.performSearch();
          }
        })
        .fontSize(14)
        .backgroundColor(this.searchType === 'content' ? $r('app.color.primary_color') : Color.Transparent)
        .fontColor(this.searchType === 'content' ? Color.White : $r('app.color.text_primary'))
        .border({
          width: 1,
          color: this.searchType === 'content' ? $r('app.color.primary_color') : $r('app.color.divider_color')
        })
        .borderRadius(16)
        .padding({ left: 16, right: 16, top: 8, bottom: 8 })
        .margin({ left: 12 })
    }
    .width('100%')
    .padding({ left: 16, right: 16, top: 12, bottom: 12 })
    .justifyContent(FlexAlign.Start)
  }

  @Builder buildLoadingView() {
    Column() {
      LoadingProgress()
        .width(40)
        .height(40)
      Text('搜索中...')
        .fontSize(14)
        .fontColor($r('app.color.text_secondary'))
        .margin({ top: 8 })
    }
    .layoutWeight(1)
    .justifyContent(FlexAlign.Center)
  }

  @Builder buildEmptyView() {
    Column() {
      Image($r('app.media.search_empty'))
        .width(120)
        .height(120)
        .opacity(0.6)
      Text('输入关键词开始搜索')
        .fontSize(16)
        .fontColor($r('app.color.text_secondary'))
        .margin({ top: 16 })
      Text('支持搜索书名、作者或书籍内容')
        .fontSize(14)
        .fontColor($r('app.color.text_secondary'))
        .margin({ top: 8 })
    }
    .layoutWeight(1)
    .justifyContent(FlexAlign.Center)
  }

  @Builder buildBookSearchResults() {
    if (this.searchResults.length === 0) {
      Column() {
        Image($r('app.media.no_results'))
          .width(100)
          .height(100)
          .opacity(0.6)
        Text('未找到相关书籍')
          .fontSize(16)
          .fontColor($r('app.color.text_secondary'))
          .margin({ top: 16 })
      }
      .layoutWeight(1)
      .justifyContent(FlexAlign.Center)
    } else {
      List({ space: 12 }) {
        ForEach(this.searchResults, (book: Book) => {
          ListItem() {
            this.buildBookItem(book)
          }
        })
      }
      .layoutWeight(1)
      .padding({ left: 16, right: 16, top: 8, bottom: 8 })
    }
  }

  @Builder buildContentSearchResults() {
    if (this.contentSearchResults.length === 0) {
      Column() {
        Image($r('app.media.no_results'))
          .width(100)
          .height(100)
          .opacity(0.6)
        Text('未找到相关内容')
          .fontSize(16)
          .fontColor($r('app.color.text_secondary'))
          .margin({ top: 16 })
      }
      .layoutWeight(1)
      .justifyContent(FlexAlign.Center)
    } else {
      List({ space: 8 }) {
        ForEach(this.contentSearchResults, (item) => {
          ListItem() {
            this.buildContentSearchItem(item)
          }
        })
      }
      .layoutWeight(1)
      .padding({ left: 16, right: 16, top: 8, bottom: 8 })
    }
  }

  @Builder buildBookItem(book: Book) {
    Row() {
      // 书籍封面
      Image(book.cover || $r('app.media.default_book_cover'))
        .width(50)
        .height(70)
        .borderRadius(4)
        .objectFit(ImageFit.Cover)

      // 书籍信息
      Column() {
        Text(book.title)
          .fontSize(16)
          .fontWeight(FontWeight.Medium)
          .fontColor($r('app.color.text_primary'))
          .maxLines(2)
          .textOverflow({ overflow: TextOverflow.Ellipsis })

        Text(book.author)
          .fontSize(14)
          .fontColor($r('app.color.text_secondary'))
          .margin({ top: 4 })

        Row() {
          Text(`${book.readingProgress}%`)
            .fontSize(12)
            .fontColor($r('app.color.primary_color'))

          Text(book.getFormattedFileSize())
            .fontSize(12)
            .fontColor($r('app.color.text_secondary'))
            .margin({ left: 12 })
        }
        .margin({ top: 8 })
      }
      .layoutWeight(1)
      .margin({ left: 12 })
      .alignItems(HorizontalAlign.Start)

      // 操作按钮
      Button('阅读')
        .onClick(() => {
          this.openBook(book);
        })
        .fontSize(12)
        .backgroundColor($r('app.color.primary_color'))
        .fontColor(Color.White)
        .width(60)
        .height(32)
    }
    .width('100%')
    .padding(12)
    .backgroundColor($r('app.color.card_background'))
    .borderRadius(8)
    .shadow({ radius: 2, color: '#1F000000', offsetX: 0, offsetY: 1 })
  }

  @Builder buildContentSearchItem(item: {book: Book, results: Array<{chapter: number, position: number, context: string}>}) {
    Column() {
      // 书籍信息
      Row() {
        Text(item.book.title)
          .fontSize(16)
          .fontWeight(FontWeight.Medium)
          .fontColor($r('app.color.text_primary'))
          .layoutWeight(1)

        Text(`${item.results.length}处匹配`)
          .fontSize(12)
          .fontColor($r('app.color.primary_color'))
      }
      .width('100%')
      .margin({ bottom: 8 })

      // 搜索结果列表
      ForEach(item.results.slice(0, 3), (result, index) => { // 只显示前3个结果
        Column() {
          Text(result.context)
            .fontSize(14)
            .fontColor($r('app.color.text_primary'))
            .maxLines(2)
            .textOverflow({ overflow: TextOverflow.Ellipsis })
            .onClick(() => {
              this.openBookAtPosition(item.book, result.chapter, result.position);
            })

          if (index < Math.min(item.results.length, 3) - 1) {
            Divider()
              .margin({ top: 8, bottom: 8 })
              .color($r('app.color.divider_color'))
          }
        }
        .width('100%')
        .alignItems(HorizontalAlign.Start)
      })

      if (item.results.length > 3) {
        Text(`还有${item.results.length - 3}个匹配结果...`)
          .fontSize(12)
          .fontColor($r('app.color.text_secondary'))
          .margin({ top: 8 })
          .onClick(() => {
            // 显示更多结果
          })
      }
    }
    .width('100%')
    .padding(12)
    .backgroundColor($r('app.color.card_background'))
    .borderRadius(8)
    .shadow({ radius: 2, color: '#1F000000', offsetX: 0, offsetY: 1 })
    .alignItems(HorizontalAlign.Start)
  }

  async performSearch() {
    if (!this.searchKeyword.trim()) {
      return;
    }

    this.isLoading = true;
    this.hasSearched = true;

    try {
      if (this.searchType === 'books') {
        await this.searchBooks();
      } else {
        await this.searchContent();
      }
    } catch (error) {
      console.error('Search failed:', error);
    } finally {
      this.isLoading = false;
    }
  }

  async searchBooks() {
    try {
      this.searchResults = await this.databaseManager.searchBooks(this.searchKeyword);
    } catch (error) {
      console.error('Failed to search books:', error);
      this.searchResults = [];
    }
  }

  async searchContent() {
    try {
      const allBooks = await this.databaseManager.getAllBooks();
      this.contentSearchResults = [];

      for (const book of allBooks) {
        try {
          const results = await this.readerController.searchInBook(book, this.searchKeyword);
          if (results.length > 0) {
            this.contentSearchResults.push({ book, results });
          }
        } catch (error) {
          console.error(`Failed to search in book ${book.title}:`, error);
        }
      }
    } catch (error) {
      console.error('Failed to search content:', error);
      this.contentSearchResults = [];
    }
  }

  openBook(book: Book) {
    router.pushUrl({
      url: 'pages/Reader',
      params: {
        bookId: book.id
      }
    });
  }

  openBookAtPosition(book: Book, chapter: number, position: number) {
    router.pushUrl({
      url: 'pages/Reader',
      params: {
        bookId: book.id,
        chapter: chapter,
        position: position
      }
    });
  }
}
