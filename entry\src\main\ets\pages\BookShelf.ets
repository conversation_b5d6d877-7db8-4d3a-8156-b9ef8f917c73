import router from '@ohos.router';
import { Book } from '../common/models/Book';
import { DatabaseManager } from '../common/database/DatabaseManager';

@Entry
@Component
struct BookShelf {
  @State books: Book[] = [];
  @State isLoading: boolean = true;
  @State selectedCategory: string = 'all';
  private databaseManager: DatabaseManager = DatabaseManager.getInstance();

  aboutToAppear() {
    this.loadBooks();
  }

  async loadBooks() {
    try {
      this.isLoading = true;
      this.books = await this.databaseManager.getAllBooks();
    } catch (error) {
      console.error('Failed to load books:', error);
    } finally {
      this.isLoading = false;
    }
  }

  build() {
    Column() {
      // 顶部标题栏
      Row() {
        Text('我的书架')
          .fontSize(20)
          .fontWeight(FontWeight.Bold)
          .layoutWeight(1)

        Button('添加书籍')
          .onClick(() => {
            this.showAddBookDialog();
          })
          .fontSize(14)
          .backgroundColor($r('app.color.primary_color'))
          .fontColor(Color.White)
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor($r('app.color.card_background'))

      // 分类筛选
      Row() {
        this.buildCategoryButton('all', '全部')
        this.buildCategoryButton('recent', '最近阅读')
        this.buildCategoryButton('favorite', '我的收藏')
        this.buildCategoryButton('finished', '已读完')
      }
      .width('100%')
      .padding({ left: 16, right: 16, top: 8, bottom: 8 })
      .justifyContent(FlexAlign.SpaceAround)

      // 书籍列表
      if (this.isLoading) {
        Column() {
          LoadingProgress()
            .width(40)
            .height(40)
          Text('加载中...')
            .fontSize(14)
            .fontColor($r('app.color.text_secondary'))
            .margin({ top: 8 })
        }
        .layoutWeight(1)
        .justifyContent(FlexAlign.Center)
      } else if (this.books.length === 0) {
        Column() {
          Image($r('app.media.empty_bookshelf'))
            .width(120)
            .height(120)
            .opacity(0.6)
          Text('书架空空如也')
            .fontSize(16)
            .fontColor($r('app.color.text_secondary'))
            .margin({ top: 16 })
          Text('点击上方"添加书籍"开始阅读吧')
            .fontSize(14)
            .fontColor($r('app.color.text_secondary'))
            .margin({ top: 8 })
        }
        .layoutWeight(1)
        .justifyContent(FlexAlign.Center)
      } else {
        this.buildBookList()
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor($r('app.color.background_color'))
  }

  @Builder buildCategoryButton(category: string, label: string) {
    Button(label)
      .onClick(() => {
        this.selectedCategory = category;
        this.filterBooks();
      })
      .fontSize(12)
      .backgroundColor(this.selectedCategory === category ? $r('app.color.primary_color') : Color.Transparent)
      .fontColor(this.selectedCategory === category ? Color.White : $r('app.color.text_primary'))
      .border({
        width: 1,
        color: this.selectedCategory === category ? $r('app.color.primary_color') : $r('app.color.divider_color')
      })
      .borderRadius(16)
      .padding({ left: 12, right: 12, top: 6, bottom: 6 })
  }

  @Builder buildBookList() {
    List({ space: 12 }) {
      ForEach(this.getFilteredBooks(), (book: Book) => {
        ListItem() {
          this.buildBookItem(book)
        }
      })
    }
    .layoutWeight(1)
    .padding({ left: 16, right: 16, top: 8, bottom: 8 })
  }

  @Builder buildBookItem(book: Book) {
    Row() {
      // 书籍封面
      Image(book.cover || $r('app.media.default_book_cover'))
        .width(60)
        .height(80)
        .borderRadius(4)
        .objectFit(ImageFit.Cover)

      // 书籍信息
      Column() {
        Text(book.title)
          .fontSize(16)
          .fontWeight(FontWeight.Medium)
          .fontColor($r('app.color.text_primary'))
          .maxLines(2)
          .textOverflow({ overflow: TextOverflow.Ellipsis })

        Text(book.author)
          .fontSize(14)
          .fontColor($r('app.color.text_secondary'))
          .margin({ top: 4 })

        Row() {
          Text(`${book.readingProgress}%`)
            .fontSize(12)
            .fontColor($r('app.color.primary_color'))

          Text(book.getFormattedLastReadTime())
            .fontSize(12)
            .fontColor($r('app.color.text_secondary'))
            .margin({ left: 12 })
        }
        .margin({ top: 8 })

        // 阅读进度条
        Progress({
          value: book.readingProgress,
          total: 100,
          type: ProgressType.Linear
        })
        .width('100%')
        .height(4)
        .margin({ top: 8 })
        .color($r('app.color.primary_color'))
        .backgroundColor($r('app.color.divider_color'))
      }
      .layoutWeight(1)
      .margin({ left: 12 })
      .alignItems(HorizontalAlign.Start)

      // 操作按钮
      Column() {
        Button('继续阅读')
          .onClick(() => {
            this.openBook(book);
          })
          .fontSize(12)
          .backgroundColor($r('app.color.primary_color'))
          .fontColor(Color.White)
          .width(80)
          .height(32)

        Button('详情')
          .onClick(() => {
            this.showBookDetail(book);
          })
          .fontSize(12)
          .backgroundColor(Color.Transparent)
          .fontColor($r('app.color.text_secondary'))
          .border({ width: 1, color: $r('app.color.divider_color') })
          .width(80)
          .height(32)
          .margin({ top: 8 })
      }
      .margin({ left: 12 })
    }
    .width('100%')
    .padding(16)
    .backgroundColor($r('app.color.card_background'))
    .borderRadius(8)
    .shadow({ radius: 2, color: '#1F000000', offsetX: 0, offsetY: 1 })
  }

  getFilteredBooks(): Book[] {
    switch (this.selectedCategory) {
      case 'recent':
        return this.books.filter(book => book.lastReadTime > 0)
          .sort((a, b) => b.lastReadTime - a.lastReadTime);
      case 'favorite':
        return this.books.filter(book => book.isFavorite);
      case 'finished':
        return this.books.filter(book => book.isFinished);
      default:
        return this.books;
    }
  }

  async filterBooks() {
    // 重新加载并筛选书籍
    await this.loadBooks();
  }

  openBook(book: Book) {
    router.pushUrl({
      url: 'pages/Reader',
      params: {
        bookId: book.id
      }
    });
  }

  showBookDetail(book: Book) {
    router.pushUrl({
      url: 'pages/BookDetail',
      params: {
        bookId: book.id
      }
    });
  }

  showAddBookDialog() {
    // 显示添加书籍对话框
    AlertDialog.show({
      title: '添加书籍',
      message: '请选择添加方式',
      primaryButton: {
        value: '本地导入',
        action: () => {
          this.importLocalBook();
        }
      },
      secondaryButton: {
        value: '在线下载',
        action: () => {
          this.downloadOnlineBook();
        }
      }
    });
  }

  async importLocalBook() {
    // 实现本地书籍导入功能
    console.info('Import local book');
  }

  async downloadOnlineBook() {
    // 实现在线书籍下载功能
    router.pushUrl({ url: 'pages/Search' });
  }
}
