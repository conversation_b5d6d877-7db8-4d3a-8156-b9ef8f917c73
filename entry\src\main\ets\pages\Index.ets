import router from '@ohos.router';

@Entry
@Component
struct Index {
  @State currentTabIndex: number = 0;
  private tabsController: TabsController = new TabsController();

  @Builder TabBuilder(title: string, targetIndex: number, selectedImg: Resource, normalImg: Resource) {
    Column() {
      Image(this.currentTabIndex === targetIndex ? selectedImg : normalImg)
        .size({ width: 25, height: 25 })
      Text(title)
        .fontColor(this.currentTabIndex === targetIndex ? '#007DFF' : '#999999')
        .fontSize(10)
        .fontWeight(this.currentTabIndex === targetIndex ? 500 : 400)
        .lineHeight(14)
        .margin({ top: 2 })
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
    .onClick(() => {
      this.currentTabIndex = targetIndex;
      this.tabsController.changeIndex(this.currentTabIndex);
    })
  }

  build() {
    Column() {
      Tabs({ barPosition: BarPosition.End, controller: this.tabsController }) {
        TabContent() {
          BookShelfPage()
        }
        .tabBar(this.TabBuilder($r('app.string.bookshelf'), 0, $r('app.media.bookshelf_selected'), $r('app.media.bookshelf_normal')))

        TabContent() {
          RecentReadingPage()
        }
        .tabBar(this.TabBuilder($r('app.string.recent_reading'), 1, $r('app.media.recent_selected'), $r('app.media.recent_normal')))

        TabContent() {
          SearchPage()
        }
        .tabBar(this.TabBuilder($r('app.string.search'), 2, $r('app.media.search_selected'), $r('app.media.search_normal')))

        TabContent() {
          SettingsPage()
        }
        .tabBar(this.TabBuilder($r('app.string.settings'), 3, $r('app.media.settings_selected'), $r('app.media.settings_normal')))
      }
      .onChange((index: number) => {
        this.currentTabIndex = index;
      })
      .width('100%')
      .height('100%')
      .barHeight(56)
    }
    .backgroundColor($r('app.color.background_color'))
  }
}

@Component
struct BookShelfPage {
  build() {
    Column() {
      Text('书架页面')
        .fontSize(20)
        .fontWeight(FontWeight.Bold)
        .margin({ top: 20 })
      
      Button('进入阅读器')
        .onClick(() => {
          router.pushUrl({ url: 'pages/Reader' });
        })
        .margin({ top: 20 })
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
  }
}

@Component
struct RecentReadingPage {
  build() {
    Column() {
      Text('最近阅读')
        .fontSize(20)
        .fontWeight(FontWeight.Bold)
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
  }
}

@Component
struct SearchPage {
  build() {
    Column() {
      Text('搜索页面')
        .fontSize(20)
        .fontWeight(FontWeight.Bold)
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
  }
}

@Component
struct SettingsPage {
  build() {
    Column() {
      Text('设置页面')
        .fontSize(20)
        .fontWeight(FontWeight.Bold)
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
  }
}
