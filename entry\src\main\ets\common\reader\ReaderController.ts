import { ReadingSettings } from '../models/ReadingSettings';
import { Book } from '../models/Book';
import { PreferencesManager } from '../preferences/PreferencesManager';
import { DatabaseManager } from '../database/DatabaseManager';
import fs from '@ohos.file.fs';

/**
 * 阅读器控制器
 */
export class ReaderController {
  private preferencesManager: PreferencesManager;
  private databaseManager: DatabaseManager;
  private currentBook: Book | null = null;
  private currentSettings: ReadingSettings;

  constructor() {
    this.preferencesManager = PreferencesManager.getInstance();
    this.databaseManager = DatabaseManager.getInstance();
    this.currentSettings = new ReadingSettings();
    this.loadReadingSettings();
  }

  /**
   * 加载阅读设置
   */
  async loadReadingSettings(): Promise<void> {
    try {
      this.currentSettings = await this.preferencesManager.getReadingSettings();
    } catch (error) {
      console.error('Failed to load reading settings:', error);
      this.currentSettings = new ReadingSettings();
    }
  }

  /**
   * 获取当前阅读设置
   */
  getReadingSettings(): ReadingSettings {
    return this.currentSettings;
  }

  /**
   * 保存阅读设置
   */
  async saveReadingSettings(settings: ReadingSettings): Promise<void> {
    try {
      settings.validate();
      this.currentSettings = settings;
      await this.preferencesManager.saveReadingSettings(settings);
    } catch (error) {
      console.error('Failed to save reading settings:', error);
      throw error;
    }
  }

  /**
   * 打开书籍
   */
  async openBook(bookId: number): Promise<Book | null> {
    try {
      const book = await this.databaseManager.getBookById(bookId);
      if (book) {
        this.currentBook = book;
        await this.preferencesManager.saveLastReadBookId(bookId);
        return book;
      }
      return null;
    } catch (error) {
      console.error('Failed to open book:', error);
      throw error;
    }
  }

  /**
   * 获取当前书籍
   */
  getCurrentBook(): Book | null {
    return this.currentBook;
  }

  /**
   * 读取书籍内容
   */
  async readBookContent(book: Book, chapterIndex: number = 0): Promise<string> {
    try {
      if (!book.filePath) {
        throw new Error('Book file path is empty');
      }

      // 检查文件是否存在
      const fileExists = await this.checkFileExists(book.filePath);
      if (!fileExists) {
        throw new Error('Book file not found');
      }

      // 根据文件类型读取内容
      switch (book.fileType.toLowerCase()) {
        case 'txt':
          return await this.readTxtFile(book.filePath);
        case 'epub':
          return await this.readEpubFile(book.filePath, chapterIndex);
        case 'pdf':
          return await this.readPdfFile(book.filePath, chapterIndex);
        default:
          throw new Error(`Unsupported file type: ${book.fileType}`);
      }
    } catch (error) {
      console.error('Failed to read book content:', error);
      throw error;
    }
  }

  /**
   * 检查文件是否存在
   */
  private async checkFileExists(filePath: string): Promise<boolean> {
    try {
      const stat = await fs.stat(filePath);
      return stat.isFile();
    } catch (error) {
      return false;
    }
  }

  /**
   * 读取TXT文件
   */
  private async readTxtFile(filePath: string): Promise<string> {
    try {
      const file = await fs.open(filePath, fs.OpenMode.READ_ONLY);
      const buffer = new ArrayBuffer(1024 * 1024); // 1MB buffer
      const readResult = await fs.read(file.fd, buffer);
      await fs.close(file.fd);
      
      // 将ArrayBuffer转换为字符串
      const decoder = new TextDecoder('utf-8');
      return decoder.decode(buffer.slice(0, readResult.bytesRead));
    } catch (error) {
      console.error('Failed to read TXT file:', error);
      throw error;
    }
  }

  /**
   * 读取EPUB文件（简化实现）
   */
  private async readEpubFile(filePath: string, chapterIndex: number): Promise<string> {
    // 这里是EPUB文件读取的简化实现
    // 实际项目中需要使用专门的EPUB解析库
    try {
      // 暂时返回示例内容
      return `EPUB文件内容 - 章节 ${chapterIndex + 1}\n\n这是EPUB格式书籍的示例内容。在实际应用中，这里会解析EPUB文件的具体章节内容。`;
    } catch (error) {
      console.error('Failed to read EPUB file:', error);
      throw error;
    }
  }

  /**
   * 读取PDF文件（简化实现）
   */
  private async readPdfFile(filePath: string, chapterIndex: number): Promise<string> {
    // 这里是PDF文件读取的简化实现
    // 实际项目中需要使用专门的PDF解析库
    try {
      // 暂时返回示例内容
      return `PDF文件内容 - 页面 ${chapterIndex + 1}\n\n这是PDF格式书籍的示例内容。在实际应用中，这里会解析PDF文件的具体页面内容。`;
    } catch (error) {
      console.error('Failed to read PDF file:', error);
      throw error;
    }
  }

  /**
   * 更新阅读进度
   */
  async updateReadingProgress(bookId: number, chapterIndex: number, position: number): Promise<void> {
    try {
      const book = await this.databaseManager.getBookById(bookId);
      if (book) {
        book.updateProgress(chapterIndex, position);
        await this.databaseManager.updateBook(book);
        
        if (this.currentBook && this.currentBook.id === bookId) {
          this.currentBook = book;
        }
      }
    } catch (error) {
      console.error('Failed to update reading progress:', error);
      throw error;
    }
  }

  /**
   * 获取书籍章节列表
   */
  async getBookChapters(book: Book): Promise<string[]> {
    try {
      // 根据文件类型获取章节列表
      switch (book.fileType.toLowerCase()) {
        case 'txt':
          return await this.getTxtChapters(book.filePath);
        case 'epub':
          return await this.getEpubChapters(book.filePath);
        case 'pdf':
          return await this.getPdfChapters(book.filePath);
        default:
          return ['第一章'];
      }
    } catch (error) {
      console.error('Failed to get book chapters:', error);
      return ['第一章'];
    }
  }

  /**
   * 获取TXT文件章节列表
   */
  private async getTxtChapters(filePath: string): Promise<string[]> {
    try {
      const content = await this.readTxtFile(filePath);
      const chapters: string[] = [];
      
      // 简单的章节识别：查找"第X章"模式
      const chapterRegex = /第[一二三四五六七八九十\d]+章[^\n]*/g;
      const matches = content.match(chapterRegex);
      
      if (matches && matches.length > 0) {
        return matches;
      } else {
        // 如果没有找到章节标记，按固定长度分割
        const chapterLength = 5000; // 每章5000字符
        const totalChapters = Math.ceil(content.length / chapterLength);
        for (let i = 0; i < totalChapters; i++) {
          chapters.push(`第${i + 1}章`);
        }
      }
      
      return chapters.length > 0 ? chapters : ['第一章'];
    } catch (error) {
      console.error('Failed to get TXT chapters:', error);
      return ['第一章'];
    }
  }

  /**
   * 获取EPUB文件章节列表
   */
  private async getEpubChapters(filePath: string): Promise<string[]> {
    // EPUB章节解析的简化实现
    return ['第一章', '第二章', '第三章', '第四章', '第五章'];
  }

  /**
   * 获取PDF文件章节列表
   */
  private async getPdfChapters(filePath: string): Promise<string[]> {
    // PDF章节解析的简化实现
    return ['第1页', '第2页', '第3页', '第4页', '第5页'];
  }

  /**
   * 搜索书籍内容
   */
  async searchInBook(book: Book, keyword: string): Promise<Array<{chapter: number, position: number, context: string}>> {
    try {
      const content = await this.readBookContent(book, 0);
      const results: Array<{chapter: number, position: number, context: string}> = [];
      
      const regex = new RegExp(keyword, 'gi');
      let match;
      
      while ((match = regex.exec(content)) !== null) {
        const start = Math.max(0, match.index - 50);
        const end = Math.min(content.length, match.index + keyword.length + 50);
        const context = content.substring(start, end);
        
        results.push({
          chapter: 0, // 简化实现，实际需要计算章节
          position: match.index,
          context: context
        });
        
        if (results.length >= 100) break; // 限制搜索结果数量
      }
      
      return results;
    } catch (error) {
      console.error('Failed to search in book:', error);
      return [];
    }
  }

  /**
   * 获取阅读统计信息
   */
  async getReadingStats(bookId: number): Promise<{totalTime: number, averageSpeed: number, lastWeekTime: number}> {
    try {
      // 这里应该从阅读历史表中统计数据
      // 简化实现，返回模拟数据
      return {
        totalTime: 3600000, // 总阅读时间（毫秒）
        averageSpeed: 300, // 平均阅读速度（字/分钟）
        lastWeekTime: 1800000 // 最近一周阅读时间（毫秒）
      };
    } catch (error) {
      console.error('Failed to get reading stats:', error);
      return {
        totalTime: 0,
        averageSpeed: 0,
        lastWeekTime: 0
      };
    }
  }
}
