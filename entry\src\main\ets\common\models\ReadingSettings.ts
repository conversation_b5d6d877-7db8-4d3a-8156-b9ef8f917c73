/**
 * 阅读设置数据模型
 */
export class ReadingSettings {
  fontSize: number = 16; // 字体大小
  lineHeight: number = 1.6; // 行高倍数
  theme: string = 'light'; // 主题：light, dark, sepia
  fontFamily: string = 'default'; // 字体族
  brightness: number = 50; // 亮度 0-100
  pageMode: string = 'scroll'; // 翻页模式：scroll, page
  autoSave: boolean = true; // 自动保存阅读进度
  fullScreen: boolean = false; // 全屏阅读
  keepScreenOn: boolean = true; // 保持屏幕常亮
  volumeKeyTurnPage: boolean = true; // 音量键翻页
  clickTurnPage: boolean = true; // 点击翻页
  animationEnabled: boolean = true; // 翻页动画
  statusBarVisible: boolean = false; // 状态栏可见
  navigationBarVisible: boolean = false; // 导航栏可见

  constructor(data?: Partial<ReadingSettings>) {
    if (data) {
      Object.assign(this, data);
    }
  }

  /**
   * 获取主题对应的背景色
   */
  getBackgroundColor(): string {
    switch (this.theme) {
      case 'dark':
        return '#1F1F1F';
      case 'sepia':
        return '#F7F3E9';
      default:
        return '#FFFFFF';
    }
  }

  /**
   * 获取主题对应的文字颜色
   */
  getTextColor(): string {
    switch (this.theme) {
      case 'dark':
        return '#E5E5E5';
      case 'sepia':
        return '#5C4B37';
      default:
        return '#182431';
    }
  }

  /**
   * 获取计算后的行高
   */
  getCalculatedLineHeight(): number {
    return this.fontSize * this.lineHeight;
  }

  /**
   * 验证设置值的有效性
   */
  validate(): boolean {
    // 字体大小范围检查
    if (this.fontSize < 12 || this.fontSize > 32) {
      this.fontSize = Math.max(12, Math.min(32, this.fontSize));
    }

    // 行高范围检查
    if (this.lineHeight < 1.0 || this.lineHeight > 3.0) {
      this.lineHeight = Math.max(1.0, Math.min(3.0, this.lineHeight));
    }

    // 亮度范围检查
    if (this.brightness < 0 || this.brightness > 100) {
      this.brightness = Math.max(0, Math.min(100, this.brightness));
    }

    // 主题值检查
    const validThemes = ['light', 'dark', 'sepia'];
    if (!validThemes.includes(this.theme)) {
      this.theme = 'light';
    }

    // 翻页模式检查
    const validPageModes = ['scroll', 'page'];
    if (!validPageModes.includes(this.pageMode)) {
      this.pageMode = 'scroll';
    }

    return true;
  }

  /**
   * 重置为默认设置
   */
  reset(): void {
    this.fontSize = 16;
    this.lineHeight = 1.6;
    this.theme = 'light';
    this.fontFamily = 'default';
    this.brightness = 50;
    this.pageMode = 'scroll';
    this.autoSave = true;
    this.fullScreen = false;
    this.keepScreenOn = true;
    this.volumeKeyTurnPage = true;
    this.clickTurnPage = true;
    this.animationEnabled = true;
    this.statusBarVisible = false;
    this.navigationBarVisible = false;
  }

  /**
   * 转换为存储格式
   */
  toStorageObject(): Record<string, any> {
    return {
      fontSize: this.fontSize,
      lineHeight: this.lineHeight,
      theme: this.theme,
      fontFamily: this.fontFamily,
      brightness: this.brightness,
      pageMode: this.pageMode,
      autoSave: this.autoSave,
      fullScreen: this.fullScreen,
      keepScreenOn: this.keepScreenOn,
      volumeKeyTurnPage: this.volumeKeyTurnPage,
      clickTurnPage: this.clickTurnPage,
      animationEnabled: this.animationEnabled,
      statusBarVisible: this.statusBarVisible,
      navigationBarVisible: this.navigationBarVisible
    };
  }

  /**
   * 从存储数据创建实例
   */
  static fromStorageObject(data: Record<string, any>): ReadingSettings {
    const settings = new ReadingSettings();
    
    if (data.fontSize !== undefined) settings.fontSize = data.fontSize;
    if (data.lineHeight !== undefined) settings.lineHeight = data.lineHeight;
    if (data.theme !== undefined) settings.theme = data.theme;
    if (data.fontFamily !== undefined) settings.fontFamily = data.fontFamily;
    if (data.brightness !== undefined) settings.brightness = data.brightness;
    if (data.pageMode !== undefined) settings.pageMode = data.pageMode;
    if (data.autoSave !== undefined) settings.autoSave = data.autoSave;
    if (data.fullScreen !== undefined) settings.fullScreen = data.fullScreen;
    if (data.keepScreenOn !== undefined) settings.keepScreenOn = data.keepScreenOn;
    if (data.volumeKeyTurnPage !== undefined) settings.volumeKeyTurnPage = data.volumeKeyTurnPage;
    if (data.clickTurnPage !== undefined) settings.clickTurnPage = data.clickTurnPage;
    if (data.animationEnabled !== undefined) settings.animationEnabled = data.animationEnabled;
    if (data.statusBarVisible !== undefined) settings.statusBarVisible = data.statusBarVisible;
    if (data.navigationBarVisible !== undefined) settings.navigationBarVisible = data.navigationBarVisible;

    settings.validate();
    return settings;
  }
}
