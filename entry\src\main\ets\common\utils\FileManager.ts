import fs from '@ohos.file.fs';
import picker from '@ohos.file.picker';
import { Book } from '../models/Book';

/**
 * 文件管理器
 */
export class FileManager {
  private static instance: FileManager;
  private readonly SUPPORTED_FORMATS = ['txt', 'epub', 'pdf'];
  private readonly BOOKS_DIR = 'books';

  private constructor() {}

  static getInstance(): FileManager {
    if (!FileManager.instance) {
      FileManager.instance = new FileManager();
    }
    return FileManager.instance;
  }

  /**
   * 初始化文件目录
   */
  async initDirectories(): Promise<void> {
    try {
      const context = globalThis.context;
      const filesDir = context.filesDir;
      const booksDir = `${filesDir}/${this.BOOKS_DIR}`;

      // 检查并创建书籍目录
      if (!await this.directoryExists(booksDir)) {
        await fs.mkdir(booksDir);
        console.info('Books directory created:', booksDir);
      }
    } catch (error) {
      console.error('Failed to initialize directories:', error);
    }
  }

  /**
   * 检查目录是否存在
   */
  private async directoryExists(path: string): Promise<boolean> {
    try {
      const stat = await fs.stat(path);
      return stat.isDirectory();
    } catch (error) {
      return false;
    }
  }

  /**
   * 检查文件是否存在
   */
  async fileExists(path: string): Promise<boolean> {
    try {
      const stat = await fs.stat(path);
      return stat.isFile();
    } catch (error) {
      return false;
    }
  }

  /**
   * 选择并导入本地文件
   */
  async pickAndImportFile(): Promise<Book | null> {
    try {
      const documentPicker = new picker.DocumentViewPicker();
      const documentSelectOptions = new picker.DocumentSelectOptions();
      documentSelectOptions.maxSelectNumber = 1;
      documentSelectOptions.fileSuffixFilters = this.SUPPORTED_FORMATS;

      const documentSelectResult = await documentPicker.select(documentSelectOptions);
      
      if (documentSelectResult && documentSelectResult.length > 0) {
        const selectedUri = documentSelectResult[0];
        return await this.importFileFromUri(selectedUri);
      }
      
      return null;
    } catch (error) {
      console.error('Failed to pick file:', error);
      throw error;
    }
  }

  /**
   * 从URI导入文件
   */
  async importFileFromUri(uri: string): Promise<Book | null> {
    try {
      // 获取文件信息
      const fileInfo = await this.getFileInfoFromUri(uri);
      if (!fileInfo) {
        throw new Error('Failed to get file info');
      }

      // 检查文件格式
      if (!this.isSupportedFormat(fileInfo.extension)) {
        throw new Error(`Unsupported file format: ${fileInfo.extension}`);
      }

      // 复制文件到应用目录
      const targetPath = await this.copyFileToAppDirectory(uri, fileInfo.name);
      
      // 创建Book对象
      const book = new Book({
        title: fileInfo.nameWithoutExtension,
        filePath: targetPath,
        fileType: fileInfo.extension,
        fileSize: fileInfo.size,
        addTime: Date.now()
      });

      // 尝试解析书籍元数据
      await this.parseBookMetadata(book);

      return book;
    } catch (error) {
      console.error('Failed to import file from URI:', error);
      throw error;
    }
  }

  /**
   * 从URI获取文件信息
   */
  private async getFileInfoFromUri(uri: string): Promise<{
    name: string;
    nameWithoutExtension: string;
    extension: string;
    size: number;
  } | null> {
    try {
      // 这里需要根据实际的URI处理逻辑来实现
      // 简化实现，实际项目中需要更复杂的URI解析
      const fileName = uri.split('/').pop() || 'unknown';
      const lastDotIndex = fileName.lastIndexOf('.');
      const nameWithoutExtension = lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName;
      const extension = lastDotIndex > 0 ? fileName.substring(lastDotIndex + 1).toLowerCase() : '';

      return {
        name: fileName,
        nameWithoutExtension,
        extension,
        size: 0 // 实际实现中需要获取真实文件大小
      };
    } catch (error) {
      console.error('Failed to get file info from URI:', error);
      return null;
    }
  }

  /**
   * 检查是否为支持的文件格式
   */
  private isSupportedFormat(extension: string): boolean {
    return this.SUPPORTED_FORMATS.includes(extension.toLowerCase());
  }

  /**
   * 复制文件到应用目录
   */
  private async copyFileToAppDirectory(sourceUri: string, fileName: string): Promise<string> {
    try {
      const context = globalThis.context;
      const filesDir = context.filesDir;
      const targetDir = `${filesDir}/${this.BOOKS_DIR}`;
      const targetPath = `${targetDir}/${fileName}`;

      // 确保目标目录存在
      if (!await this.directoryExists(targetDir)) {
        await fs.mkdir(targetDir);
      }

      // 复制文件（这里需要根据实际的文件复制API来实现）
      // 简化实现，实际项目中需要使用正确的文件复制方法
      console.info(`Copying file from ${sourceUri} to ${targetPath}`);
      
      return targetPath;
    } catch (error) {
      console.error('Failed to copy file to app directory:', error);
      throw error;
    }
  }

  /**
   * 解析书籍元数据
   */
  private async parseBookMetadata(book: Book): Promise<void> {
    try {
      switch (book.fileType.toLowerCase()) {
        case 'txt':
          await this.parseTxtMetadata(book);
          break;
        case 'epub':
          await this.parseEpubMetadata(book);
          break;
        case 'pdf':
          await this.parsePdfMetadata(book);
          break;
      }
    } catch (error) {
      console.error('Failed to parse book metadata:', error);
      // 解析失败不影响导入，使用默认值
    }
  }

  /**
   * 解析TXT文件元数据
   */
  private async parseTxtMetadata(book: Book): Promise<void> {
    try {
      // 读取文件前几行尝试提取标题和作者信息
      const content = await this.readFileContent(book.filePath, 1024); // 读取前1KB
      const lines = content.split('\n').filter(line => line.trim().length > 0);

      if (lines.length > 0) {
        // 第一行通常是标题
        const firstLine = lines[0].trim();
        if (firstLine.length > 0 && firstLine.length < 100) {
          book.title = firstLine;
        }

        // 查找作者信息
        for (const line of lines.slice(0, 5)) {
          const authorMatch = line.match(/作者[：:]\s*(.+)/);
          if (authorMatch) {
            book.author = authorMatch[1].trim();
            break;
          }
        }
      }

      // 获取文件大小
      const stat = await fs.stat(book.filePath);
      book.fileSize = stat.size;
    } catch (error) {
      console.error('Failed to parse TXT metadata:', error);
    }
  }

  /**
   * 解析EPUB文件元数据
   */
  private async parseEpubMetadata(book: Book): Promise<void> {
    try {
      // EPUB元数据解析的简化实现
      // 实际项目中需要解析EPUB的OPF文件
      book.title = book.title || 'EPUB书籍';
      book.author = book.author || '未知作者';
      
      const stat = await fs.stat(book.filePath);
      book.fileSize = stat.size;
    } catch (error) {
      console.error('Failed to parse EPUB metadata:', error);
    }
  }

  /**
   * 解析PDF文件元数据
   */
  private async parsePdfMetadata(book: Book): Promise<void> {
    try {
      // PDF元数据解析的简化实现
      // 实际项目中需要解析PDF的元数据
      book.title = book.title || 'PDF文档';
      book.author = book.author || '未知作者';
      
      const stat = await fs.stat(book.filePath);
      book.fileSize = stat.size;
    } catch (error) {
      console.error('Failed to parse PDF metadata:', error);
    }
  }

  /**
   * 读取文件内容
   */
  async readFileContent(filePath: string, maxBytes?: number): Promise<string> {
    try {
      const file = await fs.open(filePath, fs.OpenMode.READ_ONLY);
      const bufferSize = maxBytes || 1024 * 1024; // 默认1MB
      const buffer = new ArrayBuffer(bufferSize);
      const readResult = await fs.read(file.fd, buffer);
      await fs.close(file.fd);

      const decoder = new TextDecoder('utf-8');
      return decoder.decode(buffer.slice(0, readResult.bytesRead));
    } catch (error) {
      console.error('Failed to read file content:', error);
      throw error;
    }
  }

  /**
   * 删除文件
   */
  async deleteFile(filePath: string): Promise<boolean> {
    try {
      if (await this.fileExists(filePath)) {
        await fs.unlink(filePath);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Failed to delete file:', error);
      return false;
    }
  }

  /**
   * 获取文件大小
   */
  async getFileSize(filePath: string): Promise<number> {
    try {
      const stat = await fs.stat(filePath);
      return stat.size;
    } catch (error) {
      console.error('Failed to get file size:', error);
      return 0;
    }
  }

  /**
   * 获取支持的文件格式列表
   */
  getSupportedFormats(): string[] {
    return [...this.SUPPORTED_FORMATS];
  }
}
