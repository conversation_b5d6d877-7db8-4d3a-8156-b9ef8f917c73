import router from '@ohos.router';
import { ReaderController } from '../common/reader/ReaderController';
import { ReadingSettings } from '../common/models/ReadingSettings';

@Entry
@Component
struct Reader {
  @State currentChapter: string = '第一章 开始';
  @State currentContent: string = '这里是书籍内容的示例文本。在实际应用中，这里会显示从文件中读取的书籍内容。\n\n您可以通过点击屏幕来显示或隐藏阅读设置菜单。\n\n支持字体大小调整、主题切换、亮度调节等功能。';
  @State showSettings: boolean = false;
  @State readingSettings: ReadingSettings = new ReadingSettings();
  private readerController: ReaderController = new ReaderController();

  aboutToAppear() {
    // 加载阅读设置
    this.loadReadingSettings();
  }

  loadReadingSettings() {
    // 从首选项加载阅读设置
    this.readingSettings = this.readerController.getReadingSettings();
  }

  build() {
    Stack({ alignContent: Alignment.TopStart }) {
      // 主要阅读区域
      Column() {
        // 顶部标题栏
        if (this.showSettings) {
          Row() {
            Button('返回')
              .onClick(() => {
                router.back();
              })
              .backgroundColor(Color.Transparent)
              .fontColor($r('app.color.primary_color'))

            Text(this.currentChapter)
              .fontSize(16)
              .fontWeight(FontWeight.Medium)
              .layoutWeight(1)
              .textAlign(TextAlign.Center)

            Button('目录')
              .onClick(() => {
                // 显示目录
              })
              .backgroundColor(Color.Transparent)
              .fontColor($r('app.color.primary_color'))
          }
          .width('100%')
          .height(56)
          .padding({ left: 16, right: 16 })
          .backgroundColor($r('app.color.card_background'))
          .shadow({ radius: 4, color: '#1F000000', offsetX: 0, offsetY: 2 })
        }

        // 阅读内容区域
        Scroll() {
          Text(this.currentContent)
            .fontSize(this.readingSettings.fontSize)
            .lineHeight(this.readingSettings.fontSize * 1.6)
            .fontColor(this.getTextColor())
            .padding({ left: 20, right: 20, top: 20, bottom: 20 })
            .width('100%')
        }
        .layoutWeight(1)
        .onClick(() => {
          this.showSettings = !this.showSettings;
        })

        // 底部设置栏
        if (this.showSettings) {
          this.buildSettingsPanel();
        }
      }
      .width('100%')
      .height('100%')
      .backgroundColor(this.getBackgroundColor())
    }
  }

  @Builder buildSettingsPanel() {
    Column() {
      // 字体大小调节
      Row() {
        Text('字体大小')
          .fontSize(14)
          .fontColor($r('app.color.text_primary'))

        Blank()

        Row() {
          Button('A-')
            .onClick(() => {
              this.adjustFontSize(-2);
            })
            .fontSize(12)
            .backgroundColor($r('app.color.background_color'))
            .fontColor($r('app.color.text_primary'))

          Text(`${this.readingSettings.fontSize}`)
            .fontSize(14)
            .margin({ left: 12, right: 12 })

          Button('A+')
            .onClick(() => {
              this.adjustFontSize(2);
            })
            .fontSize(12)
            .backgroundColor($r('app.color.background_color'))
            .fontColor($r('app.color.text_primary'))
        }
      }
      .width('100%')
      .padding({ left: 16, right: 16, top: 12, bottom: 12 })

      Divider()
        .color($r('app.color.divider_color'))

      // 主题切换
      Row() {
        Text('阅读主题')
          .fontSize(14)
          .fontColor($r('app.color.text_primary'))

        Blank()

        Row() {
          this.buildThemeButton('light', '日间')
          this.buildThemeButton('dark', '夜间')
          this.buildThemeButton('sepia', '护眼')
        }
        .justifyContent(FlexAlign.SpaceEvenly)
      }
      .width('100%')
      .padding({ left: 16, right: 16, top: 12, bottom: 12 })
    }
    .backgroundColor($r('app.color.card_background'))
    .borderRadius({ topLeft: 12, topRight: 12 })
    .shadow({ radius: 8, color: '#1F000000', offsetX: 0, offsetY: -2 })
  }

  @Builder buildThemeButton(theme: string, label: string) {
    Button(label)
      .onClick(() => {
        this.readingSettings.theme = theme;
        this.readerController.saveReadingSettings(this.readingSettings);
      })
      .fontSize(12)
      .backgroundColor(this.readingSettings.theme === theme ? $r('app.color.primary_color') : $r('app.color.background_color'))
      .fontColor(this.readingSettings.theme === theme ? Color.White : $r('app.color.text_primary'))
      .margin({ left: 4, right: 4 })
  }

  adjustFontSize(delta: number) {
    const newSize = this.readingSettings.fontSize + delta;
    if (newSize >= 12 && newSize <= 32) {
      this.readingSettings.fontSize = newSize;
      this.readerController.saveReadingSettings(this.readingSettings);
    }
  }

  getBackgroundColor(): ResourceStr {
    switch (this.readingSettings.theme) {
      case 'dark':
        return $r('app.color.reading_background_dark');
      case 'sepia':
        return $r('app.color.reading_background_sepia');
      default:
        return $r('app.color.reading_background_light');
    }
  }

  getTextColor(): ResourceStr {
    switch (this.readingSettings.theme) {
      case 'dark':
        return $r('app.color.reading_text_dark');
      case 'sepia':
        return $r('app.color.reading_text_sepia');
      default:
        return $r('app.color.reading_text_light');
    }
  }
}
