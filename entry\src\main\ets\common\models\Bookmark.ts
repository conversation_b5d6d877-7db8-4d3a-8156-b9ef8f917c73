/**
 * 书签数据模型
 */
export class Bookmark {
  id: number = 0;
  bookId: number = 0;
  chapterIndex: number = 0;
  position: number = 0;
  content: string = '';
  note: string = '';
  createTime: number = 0;

  constructor(data?: Partial<Bookmark>) {
    if (data) {
      Object.assign(this, data);
    }
    if (this.createTime === 0) {
      this.createTime = Date.now();
    }
  }

  /**
   * 获取格式化的创建时间
   */
  getFormattedCreateTime(): string {
    return new Date(this.createTime).toLocaleString();
  }

  /**
   * 获取简短的内容预览
   */
  getContentPreview(maxLength: number = 50): string {
    if (this.content.length <= maxLength) {
      return this.content;
    }
    return this.content.substring(0, maxLength) + '...';
  }

  /**
   * 转换为数据库存储格式
   */
  toDbObject(): Record<string, any> {
    return {
      id: this.id,
      bookId: this.bookId,
      chapterIndex: this.chapterIndex,
      position: this.position,
      content: this.content,
      note: this.note,
      createTime: this.createTime
    };
  }

  /**
   * 从数据库数据创建Bookmark实例
   */
  static fromDbObject(data: Record<string, any>): Bookmark {
    const bookmark = new Bookmark();
    bookmark.id = data.id || 0;
    bookmark.bookId = data.bookId || 0;
    bookmark.chapterIndex = data.chapterIndex || 0;
    bookmark.position = data.position || 0;
    bookmark.content = data.content || '';
    bookmark.note = data.note || '';
    bookmark.createTime = data.createTime || 0;
    return bookmark;
  }
}

/**
 * 阅读历史记录数据模型
 */
export class ReadingHistory {
  id: number = 0;
  bookId: number = 0;
  chapterIndex: number = 0;
  position: number = 0;
  readTime: number = 0;
  duration: number = 0; // 阅读时长（毫秒）

  constructor(data?: Partial<ReadingHistory>) {
    if (data) {
      Object.assign(this, data);
    }
    if (this.readTime === 0) {
      this.readTime = Date.now();
    }
  }

  /**
   * 获取格式化的阅读时间
   */
  getFormattedReadTime(): string {
    return new Date(this.readTime).toLocaleString();
  }

  /**
   * 获取格式化的阅读时长
   */
  getFormattedDuration(): string {
    const minutes = Math.floor(this.duration / 60000);
    const seconds = Math.floor((this.duration % 60000) / 1000);
    
    if (minutes > 0) {
      return `${minutes}分${seconds}秒`;
    } else {
      return `${seconds}秒`;
    }
  }

  /**
   * 转换为数据库存储格式
   */
  toDbObject(): Record<string, any> {
    return {
      id: this.id,
      bookId: this.bookId,
      chapterIndex: this.chapterIndex,
      position: this.position,
      readTime: this.readTime,
      duration: this.duration
    };
  }

  /**
   * 从数据库数据创建ReadingHistory实例
   */
  static fromDbObject(data: Record<string, any>): ReadingHistory {
    const history = new ReadingHistory();
    history.id = data.id || 0;
    history.bookId = data.bookId || 0;
    history.chapterIndex = data.chapterIndex || 0;
    history.position = data.position || 0;
    history.readTime = data.readTime || 0;
    history.duration = data.duration || 0;
    return history;
  }
}
