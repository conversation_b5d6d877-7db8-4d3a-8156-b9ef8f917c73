/**
 * 书籍数据模型
 */
export class Book {
  id: number = 0;
  title: string = '';
  author: string = '';
  cover: string = '';
  filePath: string = '';
  fileType: string = ''; // txt, epub, pdf
  fileSize: number = 0;
  addTime: number = 0;
  lastReadTime: number = 0;
  readingProgress: number = 0; // 阅读进度百分比
  currentChapter: number = 0;
  currentPosition: number = 0;
  totalChapters: number = 0;
  description: string = '';
  category: string = '';
  tags: string[] = [];
  isFavorite: boolean = false;
  isFinished: boolean = false;

  constructor(data?: Partial<Book>) {
    if (data) {
      Object.assign(this, data);
    }
  }

  /**
   * 更新阅读进度
   */
  updateProgress(chapter: number, position: number) {
    this.currentChapter = chapter;
    this.currentPosition = position;
    this.lastReadTime = Date.now();
    
    if (this.totalChapters > 0) {
      this.readingProgress = Math.round((chapter / this.totalChapters) * 100);
    }
  }

  /**
   * 获取格式化的文件大小
   */
  getFormattedFileSize(): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = this.fileSize;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }

  /**
   * 获取格式化的添加时间
   */
  getFormattedAddTime(): string {
    return new Date(this.addTime).toLocaleDateString();
  }

  /**
   * 获取格式化的最后阅读时间
   */
  getFormattedLastReadTime(): string {
    if (this.lastReadTime === 0) {
      return '未阅读';
    }
    return new Date(this.lastReadTime).toLocaleDateString();
  }

  /**
   * 转换为数据库存储格式
   */
  toDbObject(): Record<string, any> {
    return {
      id: this.id,
      title: this.title,
      author: this.author,
      cover: this.cover,
      filePath: this.filePath,
      fileType: this.fileType,
      fileSize: this.fileSize,
      addTime: this.addTime,
      lastReadTime: this.lastReadTime,
      readingProgress: this.readingProgress,
      currentChapter: this.currentChapter,
      currentPosition: this.currentPosition,
      totalChapters: this.totalChapters,
      description: this.description,
      category: this.category,
      tags: JSON.stringify(this.tags),
      isFavorite: this.isFavorite ? 1 : 0,
      isFinished: this.isFinished ? 1 : 0
    };
  }

  /**
   * 从数据库数据创建Book实例
   */
  static fromDbObject(data: Record<string, any>): Book {
    const book = new Book();
    book.id = data.id || 0;
    book.title = data.title || '';
    book.author = data.author || '';
    book.cover = data.cover || '';
    book.filePath = data.filePath || '';
    book.fileType = data.fileType || '';
    book.fileSize = data.fileSize || 0;
    book.addTime = data.addTime || 0;
    book.lastReadTime = data.lastReadTime || 0;
    book.readingProgress = data.readingProgress || 0;
    book.currentChapter = data.currentChapter || 0;
    book.currentPosition = data.currentPosition || 0;
    book.totalChapters = data.totalChapters || 0;
    book.description = data.description || '';
    book.category = data.category || '';
    book.tags = data.tags ? JSON.parse(data.tags) : [];
    book.isFavorite = data.isFavorite === 1;
    book.isFinished = data.isFinished === 1;
    return book;
  }
}
