# Seeree 鸿蒙跨端阅读软件

一款专为鸿蒙系统设计的跨端阅读软件，支持多种格式的电子书阅读。

## 功能特性

### 📚 多格式支持
- **TXT文本文件**：支持各种编码格式
- **EPUB电子书**：标准EPUB格式支持
- **PDF文档**：PDF文件阅读支持

### 🎨 个性化阅读
- **多主题模式**：日间、夜间、护眼三种主题
- **字体调节**：支持字体大小、行高调整
- **阅读设置**：翻页模式、亮度调节等

### 📖 智能管理
- **书架管理**：分类整理、收藏标记
- **阅读进度**：自动保存阅读位置
- **搜索功能**：全文搜索、书籍检索

### 🔄 跨端同步
- **进度同步**：多设备阅读进度同步
- **书签同步**：书签和笔记云端保存
- **设置同步**：个人阅读偏好同步

## 技术架构

### 开发框架
- **开发语言**：ArkTS (TypeScript超集)
- **UI框架**：ArkUI
- **目标平台**：HarmonyOS

### 数据存储
- **关系型数据库**：RDB (Relational Database)
- **首选项存储**：Preferences
- **文件系统**：File System API

### 核心模块
- **阅读器引擎**：ReaderController
- **数据库管理**：DatabaseManager  
- **首选项管理**：PreferencesManager
- **文件解析**：Worker线程处理

## 项目结构

```
seeree/
├── entry/                          # 主模块
│   ├── src/main/
│   │   ├── ets/                    # ArkTS源码
│   │   │   ├── entryability/       # 应用入口
│   │   │   ├── pages/              # 页面组件
│   │   │   │   ├── Index.ets       # 主页面
│   │   │   │   ├── Reader.ets      # 阅读器页面
│   │   │   │   ├── BookShelf.ets   # 书架页面
│   │   │   │   └── Settings.ets    # 设置页面
│   │   │   ├── common/             # 公共模块
│   │   │   │   ├── models/         # 数据模型
│   │   │   │   ├── database/       # 数据库管理
│   │   │   │   ├── preferences/    # 首选项管理
│   │   │   │   └── reader/         # 阅读器控制
│   │   │   └── workers/            # Worker线程
│   │   └── resources/              # 资源文件
│   │       ├── base/
│   │       │   ├── element/        # 字符串、颜色等
│   │       │   ├── media/          # 图片资源
│   │       │   └── profile/        # 配置文件
│   │       └── ...
│   ├── build-profile.json5         # 构建配置
│   └── oh-package.json5            # 依赖配置
├── build-profile.json5             # 项目构建配置
├── hvigorfile.ts                   # 构建脚本
└── oh-package.json5                # 项目依赖配置
```

## 开发环境

### 系统要求
- **操作系统**：Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **开发工具**：DevEco Studio 4.0+
- **SDK版本**：HarmonyOS SDK API 11+

### 环境配置
1. 下载并安装 DevEco Studio
2. 配置 HarmonyOS SDK
3. 创建开发者证书
4. 配置签名信息

## 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd seeree
```

### 2. 安装依赖
```bash
# 使用DevEco Studio打开项目
# 工具会自动下载依赖
```

### 3. 配置签名
- 在DevEco Studio中配置开发者证书
- 设置应用签名信息

### 4. 运行项目
- 连接HarmonyOS设备或启动模拟器
- 点击运行按钮或使用快捷键 Ctrl+R

## 核心功能实现

### 数据模型
- **Book**：书籍信息模型
- **ReadingSettings**：阅读设置模型
- **Bookmark**：书签模型

### 数据管理
- **DatabaseManager**：数据库操作封装
- **PreferencesManager**：首选项存储管理

### 阅读引擎
- **ReaderController**：阅读器核心控制
- **文件解析**：多格式文件解析支持
- **Worker线程**：后台文件处理

## 开发计划

### 已完成功能
- ✅ 基础项目结构搭建
- ✅ 数据模型设计
- ✅ 数据库管理模块
- ✅ 首选项管理模块
- ✅ 基础UI界面
- ✅ 阅读器核心功能

### 待开发功能
- 🔄 文件导入功能
- 🔄 在线书籍下载
- 🔄 书签和笔记功能
- 🔄 阅读统计分析
- 🔄 云端同步功能
- 🔄 多语言支持

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 项目主页：[GitHub Repository]
- 问题反馈：[Issues]
- 邮箱：[<EMAIL>]

---

**注意**：本项目仍在开发中，部分功能可能不完整或存在问题。欢迎提交Issue和PR来帮助改进项目。
